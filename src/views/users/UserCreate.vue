<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Form, FormSubmitEvent } from '@primevue/forms';
import { yupResolver } from '@primevue/forms/resolvers/yup';
import * as yup from 'yup';
import { useUsersStore } from '@/stores/users';
import { useToast } from 'primevue/usetoast';
import { useTranslation } from 'i18next-vue';
import { CreateUserInput, PrivilegeOption } from '@/services/users/types';
import { ApiResponse } from '@/services/types';
import { mapErrorCode } from '@/utilities/ErrorCodeMapper';

const { t } = useTranslation();
const usersStore = useUsersStore();
const toast = useToast();

// Emits
const emit = defineEmits<{
    userCreated: [];
    cancel: [];
}>();

// Form data
const formData = ref({
    username: '',
    password: '',
    confirmPassword: '',
    privileges: [] as string[]
});

// State
const loading = ref(false);
const backendError = ref('');
const privilegeOptions = ref<PrivilegeOption[]>([]);
const profilePicture = ref<File | null>(null);
const profilePicturePreview = ref<string | null>(null);
const fileInputRef = ref<HTMLInputElement>();
const isDragOver = ref(false);

// Validation schema
const resolver = ref(
    yupResolver(
        yup.object().shape({
            username: yup.string().required('Email is required.').email('Please enter a valid email address.'),
            password: yup.string().required('Password is required.').min(6, 'Password must be at least 6 characters.'),
            confirmPassword: yup
                .string()
                .required('Please confirm your password.')
                .oneOf([yup.ref('password')], 'Passwords must match.'),
            privileges: yup.array().min(1, 'At least one privilege must be selected.')
        })
    )
);

// Load privilege options on mount
onMounted(async () => {
    const response = await usersStore.dispatchGetPrivilegeOptions();
    if (response.success) {
        privilegeOptions.value = response.content || [];
    }
});

// Handle form submission
async function handleSubmit(e: FormSubmitEvent) {
    backendError.value = '';

    if (!e.valid) {
        return;
    }

    loading.value = true;

    const createUserInput: CreateUserInput = {
        username: formData.value.username,
        password: formData.value.password,
        privileges: formData.value.privileges
    };

    const response: ApiResponse<any> = await usersStore.dispatchCreateUser(createUserInput, profilePicture.value || undefined);

    loading.value = false;

    if (response.success) {
        toast.add({
            severity: 'success',
            summary: t('success'),
            detail: t('userCreatedSuccessfully'),
            life: 3000
        });
        emit('userCreated');
    } else {
        backendError.value = mapErrorCode(response.errorResponse?.codeName);
    }
}

// Handle file upload
function onFileSelect(event: any) {
    const file = event.files ? event.files[0] : event.target.files[0];
    if (file) {
        // Check file size (2MB limit)
        const maxSize = 2 * 1024 * 1024; // 2MB in bytes
        if (file.size > maxSize) {
            toast.add({
                severity: 'error',
                summary: t('error'),
                detail: t('fileTooLarge'),
                life: 5000
            });
            return;
        }

        profilePicture.value = file;

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            profilePicturePreview.value = e.target?.result as string;
        };
        reader.readAsDataURL(file);
    }
}

// Handle drag and drop
function onDrop(event: DragEvent) {
    event.preventDefault();
    isDragOver.value = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
        const file = files[0];
        // Check if it's an image file
        if (!file.type.startsWith('image/')) {
            toast.add({
                severity: 'error',
                summary: t('error'),
                detail: t('invalidFileType'),
                life: 5000
            });
            return;
        }

        // Check file size (2MB limit)
        const maxSize = 2 * 1024 * 1024; // 2MB in bytes
        if (file.size > maxSize) {
            toast.add({
                severity: 'error',
                summary: t('error'),
                detail: t('fileTooLarge'),
                life: 5000
            });
            return;
        }

        profilePicture.value = file;

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            profilePicturePreview.value = e.target?.result as string;
        };
        reader.readAsDataURL(file);
    }
}

// Handle drag over
function onDragOver(event: DragEvent) {
    event.preventDefault();
    isDragOver.value = true;
}

// Handle drag enter
function onDragEnter(event: DragEvent) {
    event.preventDefault();
    isDragOver.value = true;
}

// Handle drag leave
function onDragLeave(event: DragEvent) {
    event.preventDefault();
    // Only set to false if we're leaving the dropzone itself, not a child element
    if (!event.currentTarget?.contains(event.relatedTarget as Node)) {
        isDragOver.value = false;
    }
}

function onFileUploadError(event: any) {
    toast.add({
        severity: 'error',
        summary: t('error'),
        detail: t('fileUploadError'),
        life: 5000
    });
}

// Handle dropzone click
function onDropzoneClick() {
    fileInputRef.value?.click();
}

// Handle file input change
function onFileInputChange(event: Event) {
    onFileSelect(event);
}

function onFileRemove() {
    profilePicture.value = null;
    profilePicturePreview.value = null;
}

// Handle cancel
function handleCancel() {
    emit('cancel');
}

// Group privileges by group
const groupedPrivileges = computed(() => {
    const groups: Record<string, PrivilegeOption[]> = {};
    privilegeOptions.value.forEach((option) => {
        if (!groups[option.group]) {
            groups[option.group] = [];
        }
        groups[option.group].push(option);
    });
    return groups;
});
</script>

<template>
    <div>
        <Form v-slot="$form" :resolver="resolver" @submit="handleSubmit">
            <div class="grid grid-cols-12 gap-6">
                <!-- Profile Picture Upload -->
                <div class="col-span-12 md:col-span-4">
                    <div class="flex flex-col items-center">
                        <!-- Circular Dropzone Container -->
                        <div class="relative w-[156px] h-[156px]">
                            <!-- Circular Dropzone -->
                            <div
                                :class="[
                                    'w-full h-full border-2 border-dashed rounded-full cursor-pointer transition-colors flex items-center justify-center overflow-hidden',
                                    isDragOver
                                        ? 'border-primary-500 bg-primary-50'
                                        : 'border-surface-300 bg-surface-50 hover:bg-surface-100'
                                ]"
                                @click="onDropzoneClick"
                                @dragover="onDragOver"
                                @dragenter="onDragEnter"
                                @dragleave="onDragLeave"
                                @drop="onDrop"
                            >
                                <!-- Preview or Upload Icon -->
                                <div v-if="profilePicturePreview" class="w-full h-full">
                                    <img :src="profilePicturePreview" alt="Profile preview" class="w-full h-full object-cover rounded-full" />
                                </div>

                                <div v-else class="flex flex-col items-center text-center p-4">
                                    <i class="pi pi-cloud-upload text-surface-400 mb-2" style="font-size: 4rem"></i>
                                    <p class="text-surface-600 text-sm leading-tight">
                                        {{ t('dragDropProfilePicture') }}
                                    </p>
                                </div>
                            </div>

                            <!-- Remove button positioned outside the circle -->
                            <button
                                v-if="profilePicturePreview"
                                type="button"
                                class="absolute -top-2 -right-2 w-8 h-8 bg-white/90 hover:bg-white text-gray-600 hover:text-gray-800 rounded-full shadow-md flex items-center justify-center transition-colors z-50"
                                @click.stop="onFileRemove"
                                :title="t('remove')"
                            >
                                <i class="pi pi-trash text-sm"></i>
                            </button>
                        </div>

                        <!-- Hidden file input -->
                        <input ref="fileInputRef" type="file" accept="image/*" class="hidden" @change="onFileInputChange" />

                        <small class="text-surface-500 mt-2 text-center">{{ t('maxFileSize2MB') }}</small>
                    </div>
                </div>

                <!-- Form Fields -->
                <div class="col-span-12 md:col-span-8">
                    <div class="grid grid-cols-12 gap-4">
                        <!-- Email -->
                        <div class="col-span-12">
                            <label for="username" class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('email') }} * </label>
                            <InputText v-model="formData.username" id="username" name="username" type="email" :placeholder="t('email')" class="mb-1" fluid />
                            <div class="h-6 relative">
                                <Message v-if="$form.username?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                                    {{ $form.username.error.message }}
                                </Message>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="col-span-12 md:col-span-6">
                            <label for="password" class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('password') }} * </label>
                            <Password v-model="formData.password" id="password" name="password" :placeholder="t('password')" class="mb-1" toggleMask fluid />
                            <div class="h-6 relative">
                                <Message v-if="$form.password?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                                    {{ $form.password.error.message }}
                                </Message>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="col-span-12 md:col-span-6">
                            <label for="confirmPassword" class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('confirmPassword') }} * </label>
                            <Password v-model="formData.confirmPassword" id="confirmPassword" name="confirmPassword" :placeholder="t('confirmPassword')" class="mb-1" toggleMask fluid :feedback="false" />
                            <div class="h-6 relative">
                                <Message v-if="$form.confirmPassword?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                                    {{ $form.confirmPassword.error.message }}
                                </Message>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privileges Section -->
                <div class="col-span-12">
                    <label class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('privileges') }} * </label>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div v-for="(options, group) in groupedPrivileges" :key="group" class="border border-surface-200 rounded-lg p-4">
                            <h4 class="text-sm font-semibold mb-3 text-surface-700">{{ t(group) }}</h4>
                            <div class="flex flex-col gap-2">
                                <div v-for="option in options" :key="option.name" class="flex items-center">
                                    <Checkbox v-model="formData.privileges" :inputId="option.name" name="privileges" :value="option.name" :disabled="option.disabled" />
                                    <label :for="option.name" class="ml-2 text-sm">
                                        {{ t(option.name) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="h-6 relative mt-2">
                        <Message v-if="$form.privileges?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                            {{ $form.privileges.error.message }}
                        </Message>
                    </div>
                </div>

                <!-- Backend Error -->
                <div class="col-span-12" v-if="backendError">
                    <Message severity="error" class="mb-4">{{ backendError }}</Message>
                </div>

                <!-- Action Buttons -->
                <div class="col-span-12">
                    <div class="flex justify-end gap-2">
                        <Button type="button" :label="t('cancel')" severity="secondary" @click="handleCancel" />
                        <Button type="submit" :label="t('createUser')" :loading="loading" />
                    </div>
                </div>
            </div>
        </Form>
    </div>
</template>
